## A<PERSON>ıklama (Türkçe)

Bu bot, **Chrome geliştirici konsolunda** çalışmaktadır.  
`order.js` dosyası, arka plandaki **WebSocket** sunucusuna bağlanarak **stok güncellemelerini** dinler.

WebSocket üzerinden stok bilgisi geldiğinde:
- `hCaptcha`, `<PERSON><PERSON><PERSON><PERSON>` gibi engelleri aşar,
- Otomatik olarak **rezervasyon** oluşturur,
- Ardından `payment.js` dosyası ile **ödeme işlemi** gerçekleştirilir,
- Bö<PERSON>ce alım işlemi tamamlanmış olur.

### Ek Bileşenler

Bu projeye aşağıdaki yardımcı Python bileşenleri eklenmiştir:

- `scraper.py`: Tesla'nın stok bilgilerini düzenli olarak tarar ve günceller.
- `wsserver.py`: WebSocket sunucusudur, ger<PERSON><PERSON> zamanlı stok verisini istemcilere iletir.
- `httpserver.py`: `date_time_fix` dosyasını sunmak için basit bir HTTP sunucusu sağlar.


---

## Description (English)

This bot runs in the **Chrome developer console**.  
The `order.js` script connects to the backend **WebSocket** server and listens for **stock updates**.

When a stock update is received:
- It bypasses protections like `hCaptcha` and `Akamai`,
- Automatically creates a **reservation**,
- Then uses `payment.js` to **process the payment**,
- Thus completing the purchase.

### Additional Components

The following helper Python components are included in this project:

- `scraper.py`: Scrapes Tesla's stock information periodically and keeps it updated.
- `wsserver.py`: A WebSocket server that delivers real-time stock updates to clients.
- `httpserver.py`: A basic HTTP server that serves the `date_time_fix` client-side script.

