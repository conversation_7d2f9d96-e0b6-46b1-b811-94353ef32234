const socketUrl = "ws://localhost:8000";
const email = "<EMAIL>";
const phoneNumber = "1111111111";
const firstName = "ismail";
const lastName = "kundakcı";
const tckn = "145314531453";
const street = "korucu";
const city = "ivrindi";
const stateProvince = "balikesir";
const zipCode = "10775";

// ===== BROWSER-SIDE MONITORING =====
const MONITOR_INTERVAL = 30000; // 30 saniye
const ENABLE_BROWSER_MONITORING = true; // Browser monitoring'i aktif et
let monitoringActive = false;
let monitoringInterval = null;

// ===== FİLTRE AYARLARI =====
const FILTER_ENABLED = true; // Filtreleme aktif
const TARGET_PRICE_MIN = 1800000; // 1.8M TL minimum
const TARGET_PRICE_MAX = 2000000; // 2.0M TL maximum
const TARGET_MODEL = "my"; // Model Y
const TARGET_TRIM = "standard"; // Standard Range

// Tesla inventory API endpoint'i
const TESLA_INVENTORY_API = "/coinorder/api/v4/inventory-results";

// Browser-side inventory monitoring function
async function checkInventory() {
    try {
        console.log("🔍 Browser-side inventory kontrolü yapılıyor...");
        
        const params = new URLSearchParams({
            query: JSON.stringify({
                query: {
                    model: "my",
                    condition: "new",
                    options: {},
                    arrangeby: "Price",
                    order: "asc",
                    market: "TR",
                    language: "tr",
                    super_region: "north america",
                    lng: "",
                    lat: "",
                    zip: "",
                    range: 0
                },
                offset: 0,
                count: 24,
                outsideOffset: 0,
                outsideSearch: false,
                isFalconDeliverySelectionEnabled: true,
                version: "v2"
            })
        });
        
        const response = await fetch(`${TESLA_INVENTORY_API}?${params}`, {
            method: 'GET',
            headers: {
                'accept': '*/*',
                'accept-language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            },
            credentials: 'include' // Önemli: session cookies'leri dahil et
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log("✅ Inventory data alındı:", data);
            
            if (data.results && data.results.length > 0) {
                console.log(`🚗 ${data.results.length} toplam araç bulundu`);
                
                // Filtreleme uygula
                let filteredCars = data.results;
                if (FILTER_ENABLED) {
                    filteredCars = data.results.filter(car => {
                        // Fiyat kontrolü
                        const price = parseFloat(car.Price) || 0;
                        const priceMatch = price >= TARGET_PRICE_MIN && price <= TARGET_PRICE_MAX;
                        
                        // Model kontrolü
                        const modelMatch = car.Model && car.Model.toLowerCase().includes(TARGET_MODEL);
                        
                        // Trim kontrolü
                        const trimMatch = (
                            (car.TrimName && car.TrimName.toLowerCase().includes(TARGET_TRIM)) ||
                            (car.TRIM && car.TRIM.toLowerCase().includes(TARGET_TRIM)) ||
                            (car.trim && car.trim.toLowerCase().includes(TARGET_TRIM)) ||
                            (car.TrimCode && car.TrimCode.toLowerCase().includes("sr")) ||
                            (price >= TARGET_PRICE_MIN && price <= TARGET_PRICE_MAX)
                        );
                        
                        const match = priceMatch && modelMatch && trimMatch;
                        
                        if (match) {
                            console.log(`✅ FİLTRE EŞLEŞMESI: VIN: ${car.VIN}, Price: ${price} TL, Model: ${car.Model}, Trim: ${car.TrimName || car.TRIM || 'N/A'}`);
                        }
                        
                        return match;
                    });
                    
                    console.log(`🎯 Filtre sonrası: ${filteredCars.length} araç (${TARGET_PRICE_MIN/1000000}M-${TARGET_PRICE_MAX/1000000}M TL Model Y Standard Range)`);
                }
                
                if (filteredCars.length > 0) {
                    console.log(`🚗 ${filteredCars.length} hedef araç bulundu!`);

                    // Her hedef araç için WebSocket'e gönder
                    filteredCars.forEach(car => {
                        try {
                            // WebSocket bağlantısı varsa gönder
                            if (window.WebSocket && socketUrl) {
                                const ws = new WebSocket(socketUrl);
                                ws.onopen = () => {
                                    ws.send(JSON.stringify(car));
                                    console.log("📤 Araç WebSocket'e gönderildi:", car.VIN);
                                    ws.close();
                                };
                            }
                            
                            // Console'da da göster
                            console.log("🚗 Bulunan araç:", {
                                VIN: car.VIN,
                                Model: car.Model,
                                Price: car.Price,
                                Location: car.DeliveryLocation
                            });
                            
                        } catch (e) {
                            console.error("❌ Araç gönderme hatası:", e);
                        }
                    });

                    // Ses çıkar (inventory bulundu)
                    try {
                        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                        audio.play().catch(() => {}); // Ses çalmazsa hata verme
                    } catch (e) {}
                    
                } else {
                    console.log("ℹ️ Filtre kriterlerine uygun araç yok");
                }
            } else {
                console.log("ℹ️ Şu anda stokta araç yok");
            }
        } else {
            console.error("❌ Inventory API hatası:", response.status, response.statusText);
        }
        
    } catch (error) {
        console.error("❌ Browser monitoring hatası:", error);
    }
}

// Browser monitoring'i başlat
function startBrowserMonitoring() {
    if (!ENABLE_BROWSER_MONITORING) {
        console.log("⚠️ Browser monitoring devre dışı");
        return;
    }
    
    if (monitoringActive) {
        console.log("⚠️ Browser monitoring zaten aktif");
        return;
    }
    
    console.log("🚀 Browser-side monitoring başlatılıyor...");
    monitoringActive = true;
    
    // İlk kontrolü hemen yap
    checkInventory();
    
    // Periyodik kontrolleri başlat
    monitoringInterval = setInterval(checkInventory, MONITOR_INTERVAL);
    
    console.log(`✅ Browser monitoring aktif (${MONITOR_INTERVAL/1000} saniye aralıklarla)`);
}

// Browser monitoring'i durdur
function stopBrowserMonitoring() {
    if (!monitoringActive) {
        console.log("⚠️ Browser monitoring zaten durmuş");
        return;
    }
    
    console.log("🛑 Browser monitoring durduruluyor...");
    monitoringActive = false;
    
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
        monitoringInterval = null;
    }
    
    console.log("✅ Browser monitoring durduruldu");
}

// Filtre kontrol fonksiyonları
function showFilterSettings() {
    console.log("🎯 Mevcut filtre ayarları:");
    console.log(`   Filtre aktif: ${FILTER_ENABLED}`);
    console.log(`   Fiyat aralığı: ${TARGET_PRICE_MIN/1000000}M - ${TARGET_PRICE_MAX/1000000}M TL`);
    console.log(`   Model: ${TARGET_MODEL}`);
    console.log(`   Trim: ${TARGET_TRIM}`);
}

function updatePriceFilter(minTL, maxTL) {
    window.TARGET_PRICE_MIN = minTL;
    window.TARGET_PRICE_MAX = maxTL;
    console.log(`✅ Fiyat filtresi güncellendi: ${minTL/1000000}M - ${maxTL/1000000}M TL`);
}

// Global fonksiyonlar olarak tanımla
window.startBrowserMonitoring = startBrowserMonitoring;
window.stopBrowserMonitoring = stopBrowserMonitoring;
window.checkInventory = checkInventory;
window.showFilterSettings = showFilterSettings;
window.updatePriceFilter = updatePriceFilter;

console.log("===== BOT AKTİF =====");
console.log("🔧 Browser monitoring komutları:");
console.log("   startBrowserMonitoring() - Monitoring'i başlat");
console.log("   stopBrowserMonitoring()  - Monitoring'i durdur");
console.log("   checkInventory()         - Tek seferlik kontrol");
console.log("   showFilterSettings()     - Filtre ayarlarını göster");
console.log("   updatePriceFilter(min,max) - Fiyat filtresi güncelle");
console.log("");
console.log("🎯 Aktif filtre: Model Y Standard Range (1.8M-2.0M TL)");

// Otomatik olarak monitoring'i başlat
if (ENABLE_BROWSER_MONITORING) {
    setTimeout(startBrowserMonitoring, 2000); // 2 saniye sonra başlat
}
