#!/usr/bin/env python3
"""
Basit Tesla Scraper - Sadece çalışan parametrelerle
"""

import requests
import json
import time
import random
from websocket import create_connection

# Konfigürasyon
scrape_do_token = "9d5faa22b8794c14ab161bd8ec3afe82eb42d37f95f"
DELAY_SECONDS = 45
REQUEST_TIMEOUT = 60

# Tesla coinorder API URL (tarayıcıdan alınan)
tesla_url = "https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22query%22%3A%7B%22model%22%3A%22my%22%2C%22condition%22%3A%22new%22%2C%22options%22%3A%7B%7D%2C%22arrangeby%22%3A%22Price%22%2C%22order%22%3A%22asc%22%2C%22market%22%3A%22TR%22%2C%22language%22%3A%22tr%22%2C%22super_region%22%3A%22north%20america%22%2C%22lng%22%3A%22%22%2C%22lat%22%3A%22%22%2C%22zip%22%3A%22%22%2C%22range%22%3A0%7D%2C%22offset%22%3A0%2C%22count%22%3A24%2C%22outsideOffset%22%3A0%2C%22outsideSearch%22%3Afalse%2C%22isFalconDeliverySelectionEnabled%22%3Atrue%2C%22version%22%3A%22v2%22%7D"

def send_simple_request():
    """En basit scrape.do isteği"""
    try:
        print("🌐 Basit scrape.do isteği gönderiliyor...")
        
        # Sadece temel parametreler
        params = {
            "token": scrape_do_token,
            "url": tesla_url
        }
        
        response = requests.get("https://api.scrape.do", params=params, timeout=REQUEST_TIMEOUT)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Başarılı yanıt alındı!")
            return response.text
        else:
            print(f"❌ Hata: {response.status_code}")
            print(f"📄 Hata detayı: {response.text[:300]}")
            return None
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout hatası!")
        return None
    except Exception as e:
        print(f"🚫 Hata: {e}")
        return None

def process_response(output):
    """Yanıtı işle ve WebSocket'e gönder"""
    if not output:
        print("⚠️ BOŞ CEVAP")
        return
    
    try:
        print("✉️ Gelen ham veri:\n", output[:500], "\n")
        
        # HTML yanıtı kontrolü
        if output.strip().startswith('<HTML>') or output.strip().startswith('<!DOCTYPE'):
            print("⚠️ HTML hata yanıtı alındı, JSON değil")
            return
        
        data = json.loads(output)
        
        # Hata kontrolü
        if "StatusCode" in data:
            status_code = data.get("StatusCode")
            if status_code == 502:
                print("⚠️ Tesla API'si geçici olarak erişilemiyor (502 Bad Gateway)")
                return
            elif status_code != 200:
                print(f"⚠️ API hatası (Status: {status_code})")
                return
        
        results = data.get("results", [])
        
        if isinstance(results, list) and results:
            # WebSocket bağlantısı kur
            try:
                ws = create_connection("ws://localhost:8000")
                for car in results:
                    item_json = json.dumps(car, ensure_ascii=False)
                    ws.send(item_json)
                    print("✅ Gönderildi:\n", item_json, "\n")
                ws.close()
            except Exception as e:
                print(f"❌ WebSocket hatası: {e}")
                # WebSocket olmasa da sonuçları göster
                for car in results:
                    print("🚗 Bulunan araç:", json.dumps(car, ensure_ascii=False, indent=2))
        else:
            print("ℹ️ 'results' dizisi yok veya boş.")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON parse hatası: {e}")
        print("📄 Ham yanıt:", output[:200])

def main():
    """Ana döngü"""
    print(f"🚀 Basit Tesla scraper başlatıldı. Her {DELAY_SECONDS} saniyede bir kontrol edilecek...")
    
    while True:
        output = send_simple_request()
        process_response(output)
        
        # Rastgele delay
        random_delay = random.randint(DELAY_SECONDS, DELAY_SECONDS + 15)
        print(f"⏳ {random_delay} saniye bekleniyor...")
        time.sleep(random_delay)

if __name__ == "__main__":
    main()
