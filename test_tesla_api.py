#!/usr/bin/env python3
"""
Tesla API Test Script - Türkiye için doğru parametreleri test eder
"""

import requests
import json

def test_tesla_turkey():
    """Tesla Türkiye API'sini test et"""
    print("🇹🇷 Tesla Türkiye API testi başlatılıyor...")
    
    url = "https://www.tesla.com/inventory/api/v4/inventory-results"
    
    # Türkiye için doğru parametreler
    params = {
        "query": json.dumps({
            "query": {
                "model": "my",  # Model Y
                "condition": "new",
                "options": {},
                "arrangeby": "Price",
                "order": "asc",
                "market": "TR",  # Türkiye
                "language": "tr",  # Türkçe
                "super_region": "europe"  # Avrupa bölge<PERSON>
            },
            "offset": 0,
            "count": 24,
            "outsideOffset": 0,
            "outsideSearch": False,
            "isFalconDeliverySelectionEnabled": True,
            "version": "v2"
        })
    }
    
    headers = {
        "accept": "*/*",
        "accept-language": "tr-TR,tr;q=0.9,en;q=0.8",
        "cache-control": "no-cache",
        "referer": "https://www.tesla.com/tr_TR/inventory/new/my",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
    }
    
    try:
        print("🌐 Tesla API'sine bağlanılıyor...")
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📏 Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            try:
                data = response.json()
                results = data.get("results", [])
                print(f"🚗 Bulunan araç sayısı: {len(results)}")
                
                if results:
                    print("✅ Tesla Türkiye API'si çalışıyor!")
                    print("📋 İlk araç bilgisi:")
                    first_car = results[0]
                    print(f"   VIN: {first_car.get('VIN', 'N/A')}")
                    print(f"   Model: {first_car.get('Model', 'N/A')}")
                    print(f"   Price: {first_car.get('Price', 'N/A')}")
                else:
                    print("ℹ️ Şu anda stokta araç yok")
                    
            except json.JSONDecodeError:
                print("❌ JSON parse hatası")
                print("📄 Ham yanıt:", response.text[:500])
        else:
            print(f"❌ API hatası: {response.status_code}")
            print("📄 Hata mesajı:", response.text[:500])
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout hatası!")
    except requests.exceptions.ConnectionError:
        print("🌐 Bağlantı hatası!")
    except Exception as e:
        print(f"🚫 Beklenmeyen hata: {e}")

if __name__ == "__main__":
    test_tesla_turkey()
