import subprocess
import time
import json
import urllib.parse
import random
from websocket import create_connection
try:
    import requests
    USE_REQUESTS = True
    # Session oluştur (cookie persistence için)
    session = requests.Session()
except ImportError:
    USE_REQUESTS = False
    session = None
    print("⚠️ requests küt<PERSON><PERSON><PERSON><PERSON> bulunamadı, curl kullanılacak.")

# WebSocket bağlantısı
ws = create_connection("ws://localhost:8000")
ws.send("scrapper started (IPv4 mode, exact only)")

# Sabit bilgiler
scrape_do_token = "9d5faa22b8794c14ab161bd8ec3afe82eb42d37f95f"
DELAY_SECONDS = 45  # İstekler arası bekleme süresi (saniye) - Tesla API'si için daha uzun
RETRY_DELAY = 60    # Rate limit durumunda bekleme süresi (saniye)
REQUEST_TIMEOUT = 60  # Request timeout süresi (saniye)
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alınan çalışan URL (coinorder API)
raw_url = "https://www.tesla.com/coinorder/api/v4/inventory-results?query=%7B%22query%22%3A%7B%22model%22%3A%22my%22%2C%22condition%22%3A%22new%22%2C%22options%22%3A%7B%7D%2C%22arrangeby%22%3A%22Price%22%2C%22order%22%3A%22asc%22%2C%22market%22%3A%22TR%22%2C%22language%22%3A%22tr%22%2C%22super_region%22%3A%22north%20america%22%2C%22lng%22%3A%22%22%2C%22lat%22%3A%22%22%2C%22zip%22%3A%22%22%2C%22range%22%3A0%7D%2C%22offset%22%3A0%2C%22count%22%3A24%2C%22outsideOffset%22%3A0%2C%22outsideSearch%22%3Afalse%2C%22isFalconDeliverySelectionEnabled%22%3Atrue%2C%22version%22%3A%22v2%22%7D"
encoded_url = urllib.parse.quote(raw_url, safe="")
# Scrape.do için basit parametreler (sadece geçerli parametreler)
scrape_params = {
    "token": scrape_do_token,
    "url": raw_url,
    "render": "false",  # Render kapalı - daha hızlı
    "premium_proxy": "true"
}
scrape_url = "https://api.scrape.do?" + urllib.parse.urlencode(scrape_params)

def test_connectivity():
    """Basit bağlantı testi"""
    if not USE_REQUESTS:
        return True

    try:
        print("🔍 Bağlantı testi yapılıyor...")
        response = requests.get("https://api.scrape.do", timeout=10)
        print(f"✅ Scrape.do erişilebilir (Status: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ Scrape.do erişilemiyor: {e}")
        return False

def simulate_browser_visit():
    """Tesla ana sayfasını ziyaret et (cookie almak için)"""
    if not USE_REQUESTS:
        return

    try:
        print("🌐 Tesla ana sayfası ziyaret ediliyor (cookie almak için)...")
        headers = {
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
            "cache-control": "no-cache",
            "dnt": "1",
            "pragma": "no-cache",
            "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        response = session.get("https://www.tesla.com/tr_TR/inventory/new/my", headers=headers, timeout=30)
        if response.status_code == 200:
            print("✅ Tesla ana sayfası ziyaret edildi, cookie'ler alındı")
        else:
            print(f"⚠️ Tesla ana sayfası ziyareti başarısız: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Ana sayfa ziyaret hatası: {e}")

def try_alternative_scraping():
    """Alternatif scraping yöntemleri dene"""
    if not USE_REQUESTS:
        return None

    print("🔄 Alternatif scraping yöntemleri deneniyor...")

    # Yöntem 1: Farklı scrape.do parametreleri
    alternative_params = {
        "token": scrape_do_token,
        "url": raw_url,
        "render": "true",
        "wait": "10000",
        "premium_proxy": "true"
    }

    alt_url = "https://api.scrape.do?" + urllib.parse.urlencode(alternative_params)

    try:
        print("🌐 ABD proxy ile deneniyor...")
        headers = {
            "accept": "application/json",
            "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        response = session.get(alt_url, headers=headers, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            print("✅ Alternatif yöntem başarılı!")
            return response.text
        else:
            print(f"⚠️ Alternatif yöntem başarısız: {response.status_code}")
    except Exception as e:
        print(f"❌ Alternatif yöntem hatası: {e}")

    return None

def try_browser_automation():
    """Tarayıcı otomasyonu simülasyonu"""
    if not USE_REQUESTS:
        return None

    print("🤖 Tarayıcı otomasyonu simülasyonu başlatılıyor...")

    # Selenium-benzeri parametreler
    browser_params = {
        "token": scrape_do_token,
        "url": "https://www.tesla.com/tr_TR/inventory/new/my",
        "render": "true",
        "wait": "15000",
        "format": "json",
        "execute": json.dumps([
            {"action": "wait", "selector": "body", "timeout": 5000},
            {"action": "click", "selector": "button[data-gtm='inventory_filter']", "optional": True},
            {"action": "wait", "timeout": 2000},
            {"action": "evaluate", "script": "fetch('/inventory/api/v4/inventory-results?query=%7B%22query%22%3A%7B%22model%22%3A%22my%22%2C%22condition%22%3A%22new%22%2C%22options%22%3A%7B%7D%2C%22arrangeby%22%3A%22Price%22%2C%22order%22%3A%22asc%22%2C%22market%22%3A%22TR%22%2C%22language%22%3A%22tr%22%2C%22super_region%22%3A%22europe%22%7D%2C%22offset%22%3A0%2C%22count%22%3A24%2C%22outsideOffset%22%3A0%2C%22outsideSearch%22%3Afalse%2C%22isFalconDeliverySelectionEnabled%22%3Atrue%2C%22version%22%3A%22v2%22%7D').then(r=>r.json())"}
        ]),
        "premium_proxy": "true"
    }

    browser_url = "https://api.scrape.do?" + urllib.parse.urlencode(browser_params)

    try:
        print("🌐 Tarayıcı otomasyonu ile API çağrısı yapılıyor...")
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        response = session.get(browser_url, headers=headers, timeout=REQUEST_TIMEOUT + 30)
        if response.status_code == 200:
            print("✅ Tarayıcı otomasyonu başarılı!")
            return response.text
        else:
            print(f"⚠️ Tarayıcı otomasyonu başarısız: {response.status_code}")
    except Exception as e:
        print(f"❌ Tarayıcı otomasyonu hatası: {e}")

    return None

def try_browser_curl_method():
    """Tarayıcıdan alınan cURL komutunu kullan"""
    if not USE_REQUESTS:
        return None

    print("🌐 Tarayıcı cURL yöntemi deneniyor...")

    # Tarayıcıdan alınan URL
    url = "https://www.tesla.com/coinorder/api/v4/inventory-results"

    # Tarayıcıdan alınan parametreler
    params = {
        "query": '{"query":{"model":"my","condition":"new","options":{},"arrangeby":"Price","order":"asc","market":"TR","language":"tr","super_region":"north america","lng":"","lat":"","zip":"","range":0},"offset":0,"count":24,"outsideOffset":0,"outsideSearch":false,"isFalconDeliverySelectionEnabled":true,"version":"v2"}'
    }

    # Tarayıcıdan alınan headers (cookies olmadan - scrape.do üzerinden)
    headers = {
        "accept": "*/*",
        "accept-language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
        "priority": "u=1, i",
        "referer": "https://www.tesla.com/tr_TR/inventory/new/my",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    # Tam URL'yi oluştur
    full_url = url + "?" + "&".join([f"{k}={v}" for k, v in params.items()])

    # Scrape.do ile dene
    scrape_params = {
        "token": scrape_do_token,
        "url": full_url,
        "render": "false",
        "premium_proxy": "true"
    }

    scrape_url = "https://api.scrape.do?" + urllib.parse.urlencode(scrape_params)

    try:
        response = session.get(scrape_url, headers={"user-agent": headers["user-agent"]}, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            print("✅ Tarayıcı cURL yöntemi başarılı!")
            return response.text
        else:
            print(f"⚠️ Tarayıcı cURL yöntemi başarısız: {response.status_code}")
            print(f"📄 Hata detayı: {response.text[:300]}")
    except Exception as e:
        print(f"❌ Tarayıcı cURL yöntemi hatası: {e}")

    return None

def send_direct_tesla_request():
    """Doğrudan Tesla API'sine istek gönder (scrape.do olmadan)"""
    if not USE_REQUESTS:
        return None

    try:
        print("🔄 Doğrudan Tesla API'sine bağlanılıyor...")
        tesla_url = "https://www.tesla.com/inventory/api/v4/inventory-results"
        params = {
            "query": json.dumps({
                "query": {
                    "model": "my",
                    "condition": "new",
                    "options": {},
                    "arrangeby": "Price",
                    "order": "asc",
                    "market": "TR",  # Türkiye için TR
                    "language": "tr",
                    "super_region": "europe"  # Türkiye için europe
                },
                "offset": 0,
                "count": 24,
                "outsideOffset": 0,
                "outsideSearch": False,
                "isFalconDeliverySelectionEnabled": True,
                "version": "v2"
            })
        }

        headers = {
            "accept": "*/*",
            "accept-language": "tr-TR,tr;q=0.9,en;q=0.8",
            "cache-control": "no-cache",
            "referer": "https://www.tesla.com/tr_TR/inventory/new/my",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }

        response = requests.get(tesla_url, params=params, headers=headers, timeout=REQUEST_TIMEOUT)
        print(f"✅ Tesla API yanıtı alındı (Status: {response.status_code})")
        return response.text
    except Exception as e:
        print(f"❌ Tesla API hatası: {e}")
        return None

def send_request_with_requests():
    """requests kütüphanesi ile istek gönder"""
    try:
        print(f"🌐 API'ye bağlanılıyor... (timeout: {REQUEST_TIMEOUT}s)")
        headers = {
            "accept": "application/json, text/plain, */*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
            "cache-control": "no-cache",
            "dnt": "1",
            "pragma": "no-cache",
            "priority": "u=1, i",
            "referer": "https://www.tesla.com/tr_TR/inventory/new/my",
            "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-requested-with": "XMLHttpRequest"
        }

        # Session kullan (cookie persistence için)
        response = session.get(scrape_url, headers=headers, timeout=REQUEST_TIMEOUT)
        print(f"✅ Yanıt alındı (Status: {response.status_code})")

        # Scrape.do'dan gelen yanıtı kontrol et
        if response.status_code != 200:
            print(f"⚠️ Scrape.do HTTP hatası: {response.status_code}")
            print(f"📄 Hata detayı: {response.text[:500]}")
            return None

        return response.text
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout hatası! API {REQUEST_TIMEOUT} saniyede yanıt vermedi.")
        return None
    except requests.exceptions.ConnectionError:
        print("🌐 Bağlantı hatası! İnternet bağlantınızı kontrol edin.")
        return None
    except requests.exceptions.RequestException as e:
        print(f"🚫 Request hatası: {e}")
        return None
    except Exception as e:
        print(f"🚫 Beklenmeyen hata: {e}")
        return None

def send_request():
    try:
        # requests kütüphanesi varsa onu kullan, yoksa curl kullan
        if USE_REQUESTS:
            # Önce tarayıcıdan alınan cURL yöntemini dene
            output = try_browser_curl_method()

            # Başarısız olursa eski yöntemleri dene
            if output is None:
                print("🔄 Tarayıcı cURL başarısız, standart scrape.do deneniyor...")
                output = send_request_with_requests()

                # Scrape.do başarısız olursa alternatif yöntemleri dene
                if output is None:
                    print("🔄 Scrape.do başarısız, alternatif yöntemler deneniyor...")
                    output = try_alternative_scraping()

                    # Alternatif yöntem de başarısız olursa tarayıcı otomasyonu dene
                    if output is None:
                        print("🤖 Son çare: Tarayıcı otomasyonu deneniyor...")
                        output = try_browser_automation()
        else:
            cmd = [
                "curl",
                "--location",
                "--compressed",
                scrape_url,
                "--header", "accept: application/json, text/plain, */*",
                "--header", "accept-encoding: gzip, deflate, br, zstd",
                "--header", "accept-language: tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
                "--header", "cache-control: no-cache",
                "--header", "dnt: 1",
                "--header", "pragma: no-cache",
                "--header", "priority: u=1, i",
                "--header", "referer: https://www.tesla.com/tr_TR/inventory/new/my",
                "--header", 'sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                "--header", "sec-ch-ua-mobile: ?0",
                "--header", 'sec-ch-ua-platform: "Windows"',
                "--header", "sec-fetch-dest: empty",
                "--header", "sec-fetch-mode: cors",
                "--header", "sec-fetch-site: same-origin",
                "--header", "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "--header", "x-requested-with: XMLHttpRequest"
            ]

            result = subprocess.run(cmd, capture_output=True, timeout=REQUEST_TIMEOUT)
            output = result.stdout.decode().strip()

        if output:
            try:
                print("✉️ Gelen ham veri:\n", output[:500], "\n")  # ilk 500 karakter

                # HTML yanıtı kontrolü (403, 502 vb. hatalar için)
                if output.strip().startswith('<HTML>') or output.strip().startswith('<!DOCTYPE'):
                    print("⚠️ HTML hata yanıtı alındı, JSON değil")
                    return

                data = json.loads(output)

                # Rate limit ve hata kontrolü
                if "StatusCode" in data:
                    status_code = data.get("StatusCode")
                    if status_code == 429:
                        print(f"⚠️ Rate limit! {RETRY_DELAY} saniye bekleniyor...")
                        time.sleep(RETRY_DELAY)
                        return
                    elif status_code == 401:
                        print("❌ Authentication hatası! Token'ı kontrol edin.")
                        return
                    elif status_code == 502:
                        print("⚠️ Tesla API'si geçici olarak erişilemiyor (502 Bad Gateway)")
                        return
                    elif status_code != 200:
                        print(f"⚠️ API hatası (Status: {status_code})")
                        return

                results = data.get("results", [])

                if isinstance(results, list) and results:
                    for car in results:
                        item_json = json.dumps(car, ensure_ascii=False)
                        ws.send(item_json)
                        print("✅ Gönderildi:\n", item_json, "\n")
                else:
                    print("ℹ️ 'results' dizisi yok veya boş.")
            except Exception as e:
                print(f"❌ JSON parse hatası: {e}")
        else:
            print("⚠️ BOŞ CEVAP")
    except subprocess.TimeoutExpired:
        print("⏰ Timeout hatası! API çok yavaş yanıt veriyor.")
    except Exception as e:
        print(f"🚫 HATA: {e}")

# Sonsuz döngüde istek gönder (delay ile)
method = "requests" if USE_REQUESTS else "curl"
print(f"🚀 Scraper başlatıldı ({method} kullanılıyor). Her {DELAY_SECONDS} saniyede bir kontrol edilecek...")

# Bağlantı testi yap
if not test_connectivity():
    print("⚠️ Bağlantı sorunları tespit edildi, yine de devam ediliyor...")

# Tesla ana sayfasını ziyaret et (cookie almak için)
simulate_browser_visit()

while True:
    send_request()

    # Rastgele delay (30-60 saniye arası) - daha doğal görünmesi için
    random_delay = random.randint(DELAY_SECONDS, DELAY_SECONDS + 15)
    print(f"⏳ {random_delay} saniye bekleniyor...")
    time.sleep(random_delay)
