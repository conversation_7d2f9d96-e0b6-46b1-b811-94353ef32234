#!/usr/bin/env python3
"""
Scrape.do Token Test Script
"""

import requests
import json

def test_scrape_do_token():
    """Scrape.do token'ını basit bir istekle test et"""
    
    token = "9d5faa22b8794c14ab161bd8ec3afe82eb42d37f95f"
    
    # Basit test URL'i
    test_url = "https://httpbin.org/json"
    
    # Scrape.do parametreleri
    params = {
        "token": token,
        "url": test_url
    }
    
    scrape_url = "https://api.scrape.do"
    
    try:
        print("🔍 Scrape.do token test ediliyor...")
        print(f"Token: {token[:20]}...")
        
        response = requests.get(scrape_url, params=params, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📏 Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            print("✅ Token çalışıyor!")
            print("📄 Yanıt:", response.text[:200])
        else:
            print("❌ Token sorunu var!")
            print("📄 Hata mesajı:", response.text[:500])
            
    except Exception as e:
        print(f"🚫 Hata: {e}")

def test_tesla_url_encoding():
    """Tesla URL encoding'ini test et"""
    
    token = "9d5faa22b8794c14ab161bd8ec3afe82eb42d37f95f"
    
    # Basit Tesla URL'i
    tesla_url = "https://www.tesla.com/tr_TR/inventory/new/my"
    
    params = {
        "token": token,
        "url": tesla_url,
        "render": "false"  # Basit test için render kapalı
    }
    
    scrape_url = "https://api.scrape.do"
    
    try:
        print("\n🚗 Tesla URL test ediliyor...")
        
        response = requests.get(scrape_url, params=params, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Tesla URL'sine erişim başarılı!")
            if "tesla" in response.text.lower():
                print("✅ Tesla sayfası içeriği alındı")
            else:
                print("⚠️ Tesla içeriği bulunamadı")
        else:
            print("❌ Tesla URL'sine erişim başarısız!")
            print("📄 Hata:", response.text[:300])
            
    except Exception as e:
        print(f"🚫 Hata: {e}")

def test_simple_tesla_api():
    """En basit Tesla API isteği"""
    
    token = "9d5faa22b8794c14ab161bd8ec3afe82eb42d37f95f"
    
    # Çok basit Tesla API URL'i
    api_url = "https://www.tesla.com/inventory/api/v4/inventory-results?query=%7B%22query%22%3A%7B%22model%22%3A%22my%22%2C%22condition%22%3A%22new%22%2C%22market%22%3A%22TR%22%7D%7D"
    
    params = {
        "token": token,
        "url": api_url
    }
    
    scrape_url = "https://api.scrape.do"
    
    try:
        print("\n🔧 Basit Tesla API test ediliyor...")
        
        response = requests.get(scrape_url, params=params, timeout=60)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Tesla API yanıtı alındı!")
            try:
                data = response.json()
                if "results" in data:
                    print(f"🚗 {len(data['results'])} araç bulundu")
                else:
                    print("📄 Yanıt:", response.text[:200])
            except:
                print("📄 Ham yanıt:", response.text[:200])
        else:
            print("❌ Tesla API başarısız!")
            print("📄 Hata:", response.text[:300])
            
    except Exception as e:
        print(f"🚫 Hata: {e}")

if __name__ == "__main__":
    print("🧪 Scrape.do test suite başlatılıyor...\n")
    
    # Test 1: Token geçerliliği
    test_scrape_do_token()
    
    # Test 2: Tesla URL erişimi
    test_tesla_url_encoding()
    
    # Test 3: Basit Tesla API
    test_simple_tesla_api()
    
    print("\n✅ Test tamamlandı!")
