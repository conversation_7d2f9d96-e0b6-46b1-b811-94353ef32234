#!/usr/bin/env python3
"""
Alternative Tesla Scraping Methods
Bu dosya farklı scraping servislerini test etmek için kullanılabilir
"""

import requests
import json
import time

def test_scrapingbee():
    """ScrapingBee servisini test et"""
    # ScrapingBee için ücretsiz API key gerekli: https://www.scrapingbee.com/
    api_key = "YOUR_SCRAPINGBEE_API_KEY"  # Buraya kendi key'inizi koyun
    
    url = "https://app.scrapingbee.com/api/v1/"
    
    params = {
        'api_key': api_key,
        'url': 'https://www.tesla.com/inventory/api/v4/inventory-results',
        'render_js': 'true',
        'premium_proxy': 'true',
        'country_code': 'tr',
        'wait': '5000',
        'custom_google': 'true'
    }
    
    # Tesla API parametreleri
    tesla_params = {
        "query": json.dumps({
            "query": {
                "model": "my",
                "condition": "new",
                "options": {},
                "arrangeby": "Price",
                "order": "asc",
                "market": "TR",
                "language": "tr",
                "super_region": "europe"
            },
            "offset": 0,
            "count": 24,
            "outsideOffset": 0,
            "outsideSearch": False,
            "isFalconDeliverySelectionEnabled": True,
            "version": "v2"
        })
    }
    
    # URL'yi Tesla parametreleri ile birleştir
    tesla_url = params['url'] + '?' + '&'.join([f"{k}={v}" for k, v in tesla_params.items()])
    params['url'] = tesla_url
    
    try:
        print("🐝 ScrapingBee ile deneniyor...")
        response = requests.get(url, params=params, timeout=60)
        
        if response.status_code == 200:
            print("✅ ScrapingBee başarılı!")
            return response.text
        else:
            print(f"❌ ScrapingBee hatası: {response.status_code}")
            print(f"Hata mesajı: {response.text}")
    except Exception as e:
        print(f"❌ ScrapingBee exception: {e}")
    
    return None

def test_brightdata():
    """Bright Data (eski Luminati) servisini test et"""
    # Bright Data için hesap gerekli: https://brightdata.com/
    
    proxy_host = "brd-customer-YOUR_CUSTOMER_ID-zone-YOUR_ZONE.zproxy.lum-superproxy.io"
    proxy_port = 22225
    proxy_user = "brd-customer-YOUR_CUSTOMER_ID-zone-YOUR_ZONE"
    proxy_pass = "YOUR_PASSWORD"
    
    proxies = {
        'http': f'http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}',
        'https': f'http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}'
    }
    
    url = "https://www.tesla.com/inventory/api/v4/inventory-results"
    
    params = {
        "query": json.dumps({
            "query": {
                "model": "my",
                "condition": "new",
                "options": {},
                "arrangeby": "Price",
                "order": "asc",
                "market": "TR",
                "language": "tr",
                "super_region": "europe"
            },
            "offset": 0,
            "count": 24,
            "outsideOffset": 0,
            "outsideSearch": False,
            "isFalconDeliverySelectionEnabled": True,
            "version": "v2"
        })
    }
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7",
        "referer": "https://www.tesla.com/tr_TR/inventory/new/my",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36"
    }
    
    try:
        print("💡 Bright Data ile deneniyor...")
        response = requests.get(url, params=params, headers=headers, proxies=proxies, timeout=60)
        
        if response.status_code == 200:
            print("✅ Bright Data başarılı!")
            return response.text
        else:
            print(f"❌ Bright Data hatası: {response.status_code}")
    except Exception as e:
        print(f"❌ Bright Data exception: {e}")
    
    return None

def test_manual_browser_simulation():
    """Manuel tarayıcı simülasyonu"""
    print("🌐 Manuel tarayıcı simülasyonu...")
    print("Bu yöntem için:")
    print("1. Chrome'u açın")
    print("2. https://www.tesla.com/tr_TR/inventory/new/my adresine gidin")
    print("3. F12 ile Developer Tools'u açın")
    print("4. Network sekmesine gidin")
    print("5. Sayfayı yenileyin ve inventory-results isteğini bulun")
    print("6. İsteğe sağ tıklayıp 'Copy as cURL' seçin")
    print("7. Bu cURL komutunu scraper'da kullanın")
    
    return None

if __name__ == "__main__":
    print("🔧 Alternatif Tesla scraping yöntemleri test ediliyor...\n")
    
    # Test ScrapingBee (API key gerekli)
    # test_scrapingbee()
    
    # Test Bright Data (hesap gerekli)  
    # test_brightdata()
    
    # Manuel yöntem açıklaması
    test_manual_browser_simulation()
