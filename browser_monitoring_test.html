<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tesla Browser Monitoring Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.inactive {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 Tesla Browser-Side Monitoring Test</h1>
        
        <div class="instructions">
            <h3>📋 Kullanım Talimatları:</h3>
            <ol>
                <li><strong>Tesla'ya giriş yapın:</strong> <a href="https://www.tesla.com/tr_TR/inventory/new/my" target="_blank">Tesla Inventory</a> sayfasına gidin ve hesabınıza giriş yapın</li>
                <li><strong>Bu sayfayı Tesla sekmesinde açın:</strong> Tesla sekmesinde F12 → Console → Bu HTML dosyasını yükleyin</li>
                <li><strong>Monitoring'i başlatın:</strong> Aşağıdaki butonları kullanın</li>
            </ol>
        </div>
        
        <div id="status" class="status inactive">
            🔴 Monitoring Durumu: Kapalı
        </div>
        
        <div>
            <button onclick="startMonitoring()">🚀 Monitoring Başlat</button>
            <button onclick="stopMonitoring()" class="danger">🛑 Monitoring Durdur</button>
            <button onclick="checkOnce()">🔍 Tek Kontrol</button>
            <button onclick="clearLog()">🗑️ Log Temizle</button>
        </div>
        
        <h3>📊 Monitoring Logs:</h3>
        <div id="log" class="log">
            Monitoring başlatılmadı...<br>
        </div>
        
        <div class="instructions">
            <h3>⚙️ Ayarlar:</h3>
            <p><strong>Kontrol Aralığı:</strong> <span id="interval">30</span> saniye</p>
            <p><strong>WebSocket URL:</strong> ws://localhost:8000</p>
            <p><strong>Tesla API:</strong> /coinorder/api/v4/inventory-results</p>
        </div>
    </div>

    <script>
        let monitoringActive = false;
        let monitoringInterval = null;
        const MONITOR_INTERVAL = 30000; // 30 saniye
        
        // Tesla inventory API endpoint'i
        const TESLA_INVENTORY_API = "/coinorder/api/v4/inventory-results";
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(active) {
            const statusDiv = document.getElementById('status');
            if (active) {
                statusDiv.className = 'status active';
                statusDiv.innerHTML = '🟢 Monitoring Durumu: Aktif';
            } else {
                statusDiv.className = 'status inactive';
                statusDiv.innerHTML = '🔴 Monitoring Durumu: Kapalı';
            }
        }
        
        async function checkInventory() {
            try {
                log("🔍 Tesla inventory kontrolü yapılıyor...");
                
                const params = new URLSearchParams({
                    query: JSON.stringify({
                        query: {
                            model: "my",
                            condition: "new",
                            options: {},
                            arrangeby: "Price",
                            order: "asc",
                            market: "TR",
                            language: "tr",
                            super_region: "north america",
                            lng: "",
                            lat: "",
                            zip: "",
                            range: 0
                        },
                        offset: 0,
                        count: 24,
                        outsideOffset: 0,
                        outsideSearch: false,
                        isFalconDeliverySelectionEnabled: true,
                        version: "v2"
                    })
                });
                
                const response = await fetch(`${TESLA_INVENTORY_API}?${params}`, {
                    method: 'GET',
                    headers: {
                        'accept': '*/*',
                        'accept-language': 'tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7',
                        'cache-control': 'no-cache',
                        'pragma': 'no-cache',
                        'sec-fetch-dest': 'empty',
                        'sec-fetch-mode': 'cors',
                        'sec-fetch-site': 'same-origin'
                    },
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.results && data.results.length > 0) {
                        log(`🚗 ${data.results.length} araç bulundu!`);
                        
                        // Ses çıkar
                        try {
                            const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                            audio.play().catch(() => {});
                        } catch (e) {}
                        
                        // Her araç için detay göster
                        data.results.forEach((car, index) => {
                            log(`   ${index + 1}. VIN: ${car.VIN || 'N/A'}, Model: ${car.Model || 'N/A'}, Price: ${car.Price || 'N/A'}`);
                        });
                        
                        // WebSocket'e göndermeyi dene
                        try {
                            const ws = new WebSocket('ws://localhost:8000');
                            ws.onopen = () => {
                                data.results.forEach(car => {
                                    ws.send(JSON.stringify(car));
                                });
                                log("📤 Araçlar WebSocket'e gönderildi");
                                ws.close();
                            };
                            ws.onerror = () => {
                                log("⚠️ WebSocket bağlantısı başarısız (wsserver.py çalışıyor mu?)");
                            };
                        } catch (e) {
                            log("⚠️ WebSocket hatası: " + e.message);
                        }
                        
                    } else {
                        log("ℹ️ Şu anda stokta araç yok");
                    }
                } else {
                    log(`❌ API hatası: ${response.status} ${response.statusText}`);
                }
                
            } catch (error) {
                log("❌ Monitoring hatası: " + error.message);
            }
        }
        
        function startMonitoring() {
            if (monitoringActive) {
                log("⚠️ Monitoring zaten aktif");
                return;
            }
            
            log("🚀 Browser-side monitoring başlatılıyor...");
            monitoringActive = true;
            updateStatus(true);
            
            // İlk kontrolü hemen yap
            checkInventory();
            
            // Periyodik kontrolleri başlat
            monitoringInterval = setInterval(checkInventory, MONITOR_INTERVAL);
            
            log(`✅ Monitoring aktif (${MONITOR_INTERVAL/1000} saniye aralıklarla)`);
        }
        
        function stopMonitoring() {
            if (!monitoringActive) {
                log("⚠️ Monitoring zaten durmuş");
                return;
            }
            
            log("🛑 Monitoring durduruluyor...");
            monitoringActive = false;
            updateStatus(false);
            
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
            
            log("✅ Monitoring durduruldu");
        }
        
        function checkOnce() {
            log("🔍 Tek seferlik kontrol yapılıyor...");
            checkInventory();
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Sayfa yüklendiğinde
        window.onload = function() {
            log("📱 Tesla Browser Monitoring Test sayfası yüklendi");
            log("💡 Tesla inventory sayfasında olduğunuzdan emin olun!");
        };
    </script>
</body>
</html>
