from http.server import HTTPServer, SimpleHTTPRequestHandler

class CORSRe<PERSON>Handler(SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', 'https://www.tesla.com')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        return super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200, "ok")
        self.end_headers()

if __name__ == '__main__':
    server = HTTPServer(('localhost', 8181), CORSRequestHandler)
    print("Serving on http://localhost:8181")
    server.serve_forever()
